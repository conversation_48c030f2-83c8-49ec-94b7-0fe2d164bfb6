import { Link } from "@remix-run/react";
import { Avatar, Avatar<PERSON>allback, But<PERSON> } from "@repo/ui-kit";
import { History, MessageSquare, Plus, Settings } from "lucide-react";

interface User {
  email?: string;
  name?: string;
}

interface SidebarProps {
  user?: User | null;
}

export function Sidebar({ user }: SidebarProps) {
  return (
    <aside className="w-72 bg-gray-50 border-r border-gray-200 flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 bg-blue-600 text-white">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
            <MessageSquare className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-lg font-bold">AI Chat Platform</h1>
        </div>
        <Button className="w-full bg-white/20 hover:bg-white/30 text-white text-sm">
          <Plus className="w-4 h-4 mr-2" />
          New Conversation
        </Button>
      </div>

      {/* Chat History */}
      <div className="flex-1 overflow-y-auto p-3">
        <div className="space-y-2">
          <div className="text-sm text-gray-600 mb-3 flex items-center font-medium">
            <History className="w-4 h-4 mr-2" />
            Recent Conversations
          </div>
          <div className="space-y-1">
            <div className="p-3 rounded-lg bg-blue-50 border border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors">
              <div className="text-sm font-semibold text-gray-900 mb-1">Current Chat</div>
              <div className="text-xs text-gray-600">AI Assistant conversation</div>
            </div>
            <div className="p-3 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
              <div className="text-sm font-semibold text-gray-900 mb-1">Getting Started</div>
              <div className="text-xs text-gray-600">How to use the platform</div>
            </div>
            <div className="p-3 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
              <div className="text-sm font-semibold text-gray-900 mb-1">Project Ideas</div>
              <div className="text-xs text-gray-600">Brainstorming session</div>
            </div>
            <div className="p-3 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
              <div className="text-sm font-semibold text-gray-900 mb-1">Code Review</div>
              <div className="text-xs text-gray-600">Technical discussion</div>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar Footer */}
      <div className="p-3 border-t border-gray-200 bg-white">
        <div className="flex items-center space-x-3 mb-3">
          {user ? (
            <>
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-blue-600 text-white font-semibold text-sm">
                  {user.email?.charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-semibold text-gray-900 truncate">{user.email}</div>
                <div className="text-xs text-green-600 flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1" />
                  Online
                </div>
              </div>
            </>
          ) : (
            <>
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-gray-400 text-white text-sm">G</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="text-sm font-semibold text-gray-900">Guest User</div>
                <div className="text-xs text-gray-500">Not signed in</div>
              </div>
            </>
          )}
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
        {!user && (
          <Link to="/login" className="block">
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm">
              Sign In to Continue
            </Button>
          </Link>
        )}
      </div>
    </aside>
  );
}
