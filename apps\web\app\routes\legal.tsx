import type { MetaFunction } from "@remix-run/cloudflare";
import { <PERSON> } from "@remix-run/react";
import { Button } from "@repo/ui-kit";
import { useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Legal - AI Chat Platform" },
    {
      name: "description",
      content: "Privacy policy, terms of service, and legal information for AI Chat platform",
    },
  ];
};

export default function Legal() {
  const [activeTab, setActiveTab] = useState("privacy");

  const tabs = [
    { id: "privacy", title: "Privacy Policy", icon: "🔒" },
    { id: "terms", title: "Terms of Service", icon: "📋" },
    { id: "cookies", title: "Cookie Policy", icon: "🍪" },
    { id: "security", title: "Security", icon: "🛡️" },
  ];

  const content = {
    privacy: {
      title: "Privacy Policy",
      lastUpdated: "December 1, 2024",
      content: (
        <div className="prose max-w-none">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">1. Information We Collect</h3>
          <p className="mb-4">
            We collect information you provide directly to us, such as when you create an account,
            use our services, or contact us for support.
          </p>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Account Information</h4>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Email address and password</li>
            <li>Profile information (name, avatar)</li>
            <li>Subscription and billing information</li>
          </ul>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Usage Information</h4>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Messages and conversations with our AI</li>
            <li>Usage patterns and preferences</li>
            <li>Device and browser information</li>
            <li>IP address and location data</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            2. How We Use Your Information
          </h3>
          <p className="mb-4">We use the information we collect to:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Provide, maintain, and improve our services</li>
            <li>Process transactions and send related information</li>
            <li>Send technical notices and support messages</li>
            <li>Respond to your comments and questions</li>
            <li>Develop new features and services</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">3. Information Sharing</h3>
          <p className="mb-4">
            We do not sell, trade, or otherwise transfer your personal information to third parties,
            except in the following circumstances:
          </p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>With your explicit consent</li>
            <li>To comply with legal obligations</li>
            <li>To protect our rights and safety</li>
            <li>With service providers who assist our operations</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">4. Data Security</h3>
          <p className="mb-6">
            We implement appropriate security measures to protect your personal information against
            unauthorized access, alteration, disclosure, or destruction. This includes encryption,
            secure server infrastructure, and regular security audits.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">5. Your Rights</h3>
          <p className="mb-4">You have the right to:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Access your personal information</li>
            <li>Correct inaccurate data</li>
            <li>Delete your account and data</li>
            <li>Export your data</li>
            <li>Opt out of certain communications</li>
          </ul>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-blue-800">
              <strong>Contact Us:</strong> If you have questions about this Privacy Policy, please
              contact <NAME_EMAIL>
            </p>
          </div>
        </div>
      ),
    },
    terms: {
      title: "Terms of Service",
      lastUpdated: "December 1, 2024",
      content: (
        <div className="prose max-w-none">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h3>
          <p className="mb-6">
            By accessing and using AI Chat, you accept and agree to be bound by the terms and
            provision of this agreement. These Terms of Service apply to all users of the service.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">2. Description of Service</h3>
          <p className="mb-4">
            AI Chat is an artificial intelligence-powered conversation platform that provides:
          </p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Interactive AI conversations</li>
            <li>Conversation history and management</li>
            <li>API access for developers</li>
            <li>Premium features for subscribers</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">3. User Responsibilities</h3>
          <p className="mb-4">Users are responsible for:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Maintaining the confidentiality of their account</li>
            <li>Using the service in compliance with applicable laws</li>
            <li>Not engaging in harmful or abusive behavior</li>
            <li>Respecting intellectual property rights</li>
            <li>Not attempting to circumvent security measures</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">4. Prohibited Uses</h3>
          <p className="mb-4">You may not use our service to:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Generate harmful, illegal, or abusive content</li>
            <li>Impersonate others or provide false information</li>
            <li>Attempt to reverse engineer our AI models</li>
            <li>Violate any applicable laws or regulations</li>
            <li>Interfere with the proper working of the service</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">5. Service Availability</h3>
          <p className="mb-6">
            We strive to maintain high availability but cannot guarantee uninterrupted service. We
            reserve the right to modify, suspend, or discontinue the service with reasonable notice.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">6. Limitation of Liability</h3>
          <p className="mb-6">
            Our liability is limited to the maximum extent permitted by law. We are not responsible
            for indirect, incidental, or consequential damages arising from your use of the service.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">7. Changes to Terms</h3>
          <p className="mb-6">
            We may update these terms periodically. We will notify users of significant changes via
            email or through the service. Continued use constitutes acceptance of updated terms.
          </p>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">
              <strong>Important:</strong> Violation of these terms may result in account suspension
              or termination. Please review these terms carefully.
            </p>
          </div>
        </div>
      ),
    },
    cookies: {
      title: "Cookie Policy",
      lastUpdated: "December 1, 2024",
      content: (
        <div className="prose max-w-none">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">What Are Cookies?</h3>
          <p className="mb-6">
            Cookies are small text files that are placed on your computer or mobile device when you
            visit a website. They help websites remember your preferences and improve your
            experience.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">How We Use Cookies</h3>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Essential Cookies</h4>
          <p className="mb-4">These cookies are necessary for the website to function properly:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Authentication and session management</li>
            <li>Security and fraud prevention</li>
            <li>Basic website functionality</li>
          </ul>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Analytics Cookies</h4>
          <p className="mb-4">These cookies help us understand how visitors use our website:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Page views and user interactions</li>
            <li>Performance and error tracking</li>
            <li>Usage patterns and trends</li>
          </ul>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Preference Cookies</h4>
          <p className="mb-4">These cookies remember your preferences:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Language and region settings</li>
            <li>Theme and display preferences</li>
            <li>Customization options</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Managing Cookies</h3>
          <p className="mb-4">
            You can control cookies through your browser settings. However, disabling certain
            cookies may affect the functionality of our website.
          </p>

          <div className="bg-gray-100 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-gray-900 mb-2">Browser Settings</h4>
            <ul className="text-sm space-y-1">
              <li>
                <strong>Chrome:</strong> Settings → Privacy and security → Cookies
              </li>
              <li>
                <strong>Firefox:</strong> Options → Privacy & Security → Cookies
              </li>
              <li>
                <strong>Safari:</strong> Preferences → Privacy → Cookies
              </li>
              <li>
                <strong>Edge:</strong> Settings → Cookies and site permissions
              </li>
            </ul>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Third-Party Cookies</h3>
          <p className="mb-6">
            We may use third-party services that place cookies on your device. These include
            analytics providers and payment processors. Please refer to their privacy policies for
            information about their cookie practices.
          </p>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-800">
              <strong>Your Choice:</strong> By continuing to use our website, you consent to our use
              of cookies as described in this policy.
            </p>
          </div>
        </div>
      ),
    },
    security: {
      title: "Security",
      lastUpdated: "December 1, 2024",
      content: (
        <div className="prose max-w-none">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Our Security Commitment</h3>
          <p className="mb-6">
            Security is fundamental to everything we do at AI Chat. We implement multiple layers of
            protection to keep your data safe and secure.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Data Protection</h3>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Encryption</h4>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>All data transmitted to and from our servers is encrypted using TLS 1.3</li>
            <li>Data at rest is encrypted using AES-256 encryption</li>
            <li>Database encryption with rotating keys</li>
            <li>End-to-end encryption for sensitive communications</li>
          </ul>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Infrastructure Security</h4>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Cloud infrastructure with enterprise-grade security</li>
            <li>Regular security audits and penetration testing</li>
            <li>DDoS protection and traffic filtering</li>
            <li>Secure development practices and code reviews</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Access Controls</h3>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Authentication</h4>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Strong password requirements</li>
            <li>Two-factor authentication (2FA) support</li>
            <li>OAuth integration with trusted providers</li>
            <li>Session management and automatic logout</li>
          </ul>

          <h4 className="text-lg font-semibold text-gray-900 mb-3">Authorization</h4>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Role-based access control (RBAC)</li>
            <li>Principle of least privilege</li>
            <li>Regular access reviews and updates</li>
            <li>Audit logging for all access attempts</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Compliance & Certifications</h3>
          <p className="mb-4">We maintain compliance with industry standards:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>SOC 2 Type II certification</li>
            <li>GDPR compliance for EU users</li>
            <li>CCPA compliance for California residents</li>
            <li>Regular third-party security assessments</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Incident Response</h3>
          <p className="mb-4">In the event of a security incident:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>24/7 monitoring and alerting systems</li>
            <li>Dedicated incident response team</li>
            <li>Rapid containment and remediation procedures</li>
            <li>Transparent communication with affected users</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Your Security</h3>
          <p className="mb-4">You can help protect your account by:</p>
          <ul className="list-disc list-inside mb-6 space-y-1">
            <li>Using a strong, unique password</li>
            <li>Enabling two-factor authentication</li>
            <li>Regularly reviewing your account activity</li>
            <li>Reporting suspicious activity immediately</li>
          </ul>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <p className="text-blue-800">
              <strong>Security Contact:</strong> If you discover a security vulnerability, please
              report <NAME_EMAIL>
            </p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <p className="text-green-800">
              <strong>Bug Bounty:</strong> We operate a responsible disclosure program and reward
              security researchers who help us improve our security.
            </p>
          </div>
        </div>
      ),
    },
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/landing" className="text-2xl font-bold text-gray-900">
                AI Chat
              </Link>
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/about" className="text-gray-600 hover:text-gray-900">
                About
              </Link>
              <Link to="/blog" className="text-gray-600 hover:text-gray-900">
                Blog
              </Link>
              <Link to="/docs" className="text-gray-600 hover:text-gray-900">
                Docs
              </Link>
              <Link to="/login" className="text-gray-600 hover:text-gray-900">
                Login
              </Link>
              <Link to="/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-gray-50 to-gray-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Legal Information</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our commitment to transparency, privacy, and security. Learn about your rights and our
              responsibilities.
            </p>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Tabs */}
          <div className="border-b border-gray-200 mb-8">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.title}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="max-w-4xl">
            <div className="mb-6">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                {content[activeTab as keyof typeof content].title}
              </h2>
              <p className="text-sm text-gray-500">
                Last updated: {content[activeTab as keyof typeof content].lastUpdated}
              </p>
            </div>

            <div className="bg-white">{content[activeTab as keyof typeof content].content}</div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Questions About Our Legal Policies?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            We're here to help. Contact our legal team for any questions or concerns.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button className="px-6 py-3">Contact Legal Team</Button>
            <Button variant="outline" className="px-6 py-3">
              Download Policies (PDF)
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">AI Chat</h3>
              <p className="text-gray-400">
                The most advanced AI conversation platform built for the modern world.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/landing" className="hover:text-white">
                    Features
                  </Link>
                </li>
                <li>
                  <Link to="/docs" className="hover:text-white">
                    Documentation
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    API
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Pricing
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link to="/about" className="hover:text-white">
                    About
                  </Link>
                </li>
                <li>
                  <Link to="/blog" className="hover:text-white">
                    Blog
                  </Link>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Careers
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <button onClick={() => setActiveTab("privacy")} className="hover:text-white">
                    Privacy Policy
                  </button>
                </li>
                <li>
                  <button onClick={() => setActiveTab("terms")} className="hover:text-white">
                    Terms of Service
                  </button>
                </li>
                <li>
                  <button onClick={() => setActiveTab("cookies")} className="hover:text-white">
                    Cookie Policy
                  </button>
                </li>
                <li>
                  <button onClick={() => setActiveTab("security")} className="hover:text-white">
                    Security
                  </button>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2025 AI Chat. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
