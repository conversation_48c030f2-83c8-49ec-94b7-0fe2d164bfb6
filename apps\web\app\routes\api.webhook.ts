import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    throw new Response("Method not allowed", { status: 405 });
  }

  try {
    const payload = await request.json();
    const env = (context.cloudflare as { env: Record<string, any> }).env;

    // Log webhook payload
    console.log("Webhook received:", payload);

    // Store in KV for processing (if KV is available)
    if (env.CACHE) {
      await env.CACHE.put(
        `webhook:${Date.now()}`,
        JSON.stringify(payload),
        { expirationTtl: 86400 } // 24 hours
      );
    }

    return json({
      status: "received",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Webhook error:", error);
    throw new Response("Invalid webhook payload", { status: 400 });
  }
}
