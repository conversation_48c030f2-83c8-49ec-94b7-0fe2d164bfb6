import { Link } from "@remix-run/react";
import { Button } from "@repo/ui-kit";
import { Bo<PERSON>, ArrowR<PERSON> } from "lucide-react";

interface User {
  email?: string;
  name?: string;
}

interface ChatHeaderProps {
  user?: User | null;
}

export function ChatHeader({ user }: ChatHeaderProps) {
  return (
    <header className="bg-blue-600 text-white p-4 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
            <Bot className="w-5 h-5" />
          </div>
          <div>
            <h2 className="text-lg font-bold">AI Assistant</h2>
            <p className="text-blue-100 flex items-center text-sm">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              Always ready to help
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {user && (
            <Link to="/dashboard">
              <Button variant="outline" className="border-white/30 text-white hover:bg-white/20 text-sm">
                Dashboard
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </Link>
          )}
          <div className="text-right">
            <div className="text-sm font-medium">
              {user ? user.email : "Guest User"}
            </div>
            <div className="text-xs text-blue-100">
              {user ? "Premium User" : "Free Trial"}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
