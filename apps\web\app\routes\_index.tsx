import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Link, useLoaderData } from "@remix-run/react";
import { useState } from "react";
import {
  Avatar,
  AvatarFallback,
  Button,
  Textarea,
} from "@repo/ui-kit";
import {
  ArrowRight,
  MessageSquare,
  Send,
  Settings,
  Bot,
  User,
  History,
  Plus
} from "lucide-react";
import { getUser } from "~/lib/auth.server";
import { Showcase } from "~/components/showcase";

export const meta: MetaFunction = () => {
  return [
    { title: "AI Chat Platform - The Future of AI Conversations" },
    {
      name: "description",
      content:
        "Experience intelligent, context-aware conversations with our advanced AI assistant. Get instant answers, creative solutions, and personalized assistance.",
    },
  ];
};

export async function loader({ request, context }: LoaderFunctionArgs) {
  const user = await getUser(request, context);

  return json({
    user,
  });
}

type Message = {
  id: number;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
};

export default function Index() {
  const { user } = useLoaderData<typeof loader>();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      type: "assistant" as const,
      content: "Hello! I'm your AI assistant. How can I help you today?",
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: messages.length + 1,
      type: "user",
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: messages.length + 2,
        type: "assistant",
        content: "Thanks for your message! This is a demo response. In a real implementation, this would connect to your AI service.",
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-white flex">
      {/* Sidebar */}
      <aside className="w-72 bg-gray-50 border-r border-gray-200 flex flex-col">
        {/* Sidebar Header */}
        <div className="p-4 bg-blue-600 text-white">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-lg font-bold">AI Chat Platform</h1>
          </div>
          <Button className="w-full bg-white/20 hover:bg-white/30 text-white text-sm">
            <Plus className="w-4 h-4 mr-2" />
            New Conversation
          </Button>
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-y-auto p-3">
          <div className="space-y-2">
            <div className="text-sm text-gray-600 mb-3 flex items-center font-medium">
              <History className="w-4 h-4 mr-2" />
              Recent Conversations
            </div>
            <div className="space-y-1">
              <div className="p-3 rounded-lg bg-blue-50 border border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors">
                <div className="text-sm font-semibold text-gray-900 mb-1">Current Chat</div>
                <div className="text-xs text-gray-600">AI Assistant conversation</div>
              </div>
              <div className="p-3 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="text-sm font-semibold text-gray-900 mb-1">Getting Started</div>
                <div className="text-xs text-gray-600">How to use the platform</div>
              </div>
              <div className="p-3 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="text-sm font-semibold text-gray-900 mb-1">Project Ideas</div>
                <div className="text-xs text-gray-600">Brainstorming session</div>
              </div>
              <div className="p-3 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="text-sm font-semibold text-gray-900 mb-1">Code Review</div>
                <div className="text-xs text-gray-600">Technical discussion</div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar Footer */}
        <div className="p-3 border-t border-gray-200 bg-white">
          <div className="flex items-center space-x-3 mb-3">
            {user ? (
              <>
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-blue-600 text-white font-semibold text-sm">
                    {user.email?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-semibold text-gray-900 truncate">{user.email}</div>
                  <div className="text-xs text-green-600 flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                    Online
                  </div>
                </div>
              </>
            ) : (
              <>
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-gray-400 text-white text-sm">G</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="text-sm font-semibold text-gray-900">Guest User</div>
                  <div className="text-xs text-gray-500">Not signed in</div>
                </div>
              </>
            )}
            <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
          {!user && (
            <Link to="/login" className="block">
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm">
                Sign In to Continue
              </Button>
            </Link>
          )}
        </div>
      </aside>

      {/* Main Content Area */}
      <main className="flex-1 flex flex-col bg-white overflow-hidden">
        {/* AI Chat Section */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Chat Header */}
          <header className="bg-blue-600 text-white p-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <Bot className="w-5 h-5" />
                </div>
                <div>
                  <h2 className="text-lg font-bold">AI Assistant</h2>
                  <p className="text-blue-100 flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                    Always ready to help
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                {user && (
                  <Link to="/dashboard">
                    <Button variant="outline" className="border-white/30 text-white hover:bg-white/20 text-sm">
                      Dashboard
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                )}
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {user ? user.email : "Guest User"}
                  </div>
                  <div className="text-xs text-blue-100">
                    {user ? "Premium User" : "Free Trial"}
                  </div>
                </div>
              </div>
            </div>
          </header>

          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto bg-gray-50 min-h-0">
            <div className="p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
                >
                  <div
                    className={`flex items-start space-x-3 max-w-2xl ${
                      message.type === "user" ? "flex-row-reverse space-x-reverse" : ""
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                      message.type === "user"
                        ? "bg-blue-600"
                        : "bg-gray-600"
                    }`}>
                      {message.type === "user" ? (
                        <User className="w-4 h-4 text-white" />
                      ) : (
                        <Bot className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div
                      className={`rounded-lg p-3 ${
                        message.type === "user"
                          ? "bg-blue-600 text-white"
                          : "bg-white border border-gray-200"
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                      <p className={`text-xs mt-1 ${
                        message.type === "user" ? "text-blue-100" : "text-gray-500"
                      }`}>
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <div className="flex items-start space-x-3 max-w-2xl">
                    <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <Bot className="w-4 h-4 text-white" />
                    </div>
                    <div className="rounded-lg p-3 bg-white border border-gray-200">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.1s" }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Message Input */}
          <div className="p-4 bg-white border-t border-gray-200">
            <div className="flex space-x-3">
              <div className="flex-1">
                <Textarea
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  placeholder="Ask me anything... I'm here to help!"
                  className="min-h-[50px] max-h-32 resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                />
              </div>
              <Button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim() || isLoading}
                className="self-end h-[50px] px-4 bg-blue-600 hover:bg-blue-700 rounded-lg"
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>
            <div className="text-center mt-2">
              <p className="text-xs text-gray-500">
                Press Enter to send, Shift+Enter for new line • Powered by AI
              </p>
            </div>
          </div>
        </div>

        {/* Showcase Section */}
        <div className="flex-1 overflow-y-auto bg-gray-50 border-t border-gray-200">
          <Showcase />
        </div>
      </main>
    </div>
  );
}
