@import "tailwindcss";

/* Tailwind 4.0 Theme Configuration */
@theme {
  /* Colors - Optimized for American users with modern, clean palette */
  --color-background: 0 0% 100%;
  --color-foreground: 240 10% 3.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 240 10% 3.9%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 240 10% 3.9%;
  --color-primary: 240 5.9% 10%;
  --color-primary-foreground: 0 0% 98%;
  --color-secondary: 240 4.8% 95.9%;
  --color-secondary-foreground: 240 5.9% 10%;
  --color-muted: 240 4.8% 95.9%;
  --color-muted-foreground: 240 3.8% 46.1%;
  --color-accent: 240 4.8% 95.9%;
  --color-accent-foreground: 240 5.9% 10%;
  --color-destructive: 0 84.2% 60.2%;
  --color-destructive-foreground: 0 0% 98%;
  --color-border: 240 5.9% 90%;
  --color-input: 240 5.9% 90%;
  --color-ring: 240 5.9% 10%;

  /* Dark mode colors */
  --color-dark-background: 240 10% 3.9%;
  --color-dark-foreground: 0 0% 98%;
  --color-dark-card: 240 10% 3.9%;
  --color-dark-card-foreground: 0 0% 98%;
  --color-dark-popover: 240 10% 3.9%;
  --color-dark-popover-foreground: 0 0% 98%;
  --color-dark-primary: 0 0% 98%;
  --color-dark-primary-foreground: 240 5.9% 10%;
  --color-dark-secondary: 240 3.7% 15.9%;
  --color-dark-secondary-foreground: 0 0% 98%;
  --color-dark-muted: 240 3.7% 15.9%;
  --color-dark-muted-foreground: 240 5% 64.9%;
  --color-dark-accent: 240 3.7% 15.9%;
  --color-dark-accent-foreground: 0 0% 98%;
  --color-dark-destructive: 0 62.8% 30.6%;
  --color-dark-destructive-foreground: 0 0% 98%;
  --color-dark-border: 240 3.7% 15.9%;
  --color-dark-input: 240 3.7% 15.9%;
  --color-dark-ring: 240 4.9% 83.9%;

  /* Border radius - Compact design preference */
  --radius: 0.375rem;

  /* Typography - Inter font for American users */
  --font-sans: "Inter", ui-sans-serif, system-ui, sans-serif;
  --font-mono: ui-monospace, "SF Mono", "Cascadia Code", "Roboto Mono", monospace;
}

/* Dark mode theme variables */
@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: var(--color-dark-background);
    --color-foreground: var(--color-dark-foreground);
    --color-card: var(--color-dark-card);
    --color-card-foreground: var(--color-dark-card-foreground);
    --color-popover: var(--color-dark-popover);
    --color-popover-foreground: var(--color-dark-popover-foreground);
    --color-primary: var(--color-dark-primary);
    --color-primary-foreground: var(--color-dark-primary-foreground);
    --color-secondary: var(--color-dark-secondary);
    --color-secondary-foreground: var(--color-dark-secondary-foreground);
    --color-muted: var(--color-dark-muted);
    --color-muted-foreground: var(--color-dark-muted-foreground);
    --color-accent: var(--color-dark-accent);
    --color-accent-foreground: var(--color-dark-accent-foreground);
    --color-destructive: var(--color-dark-destructive);
    --color-destructive-foreground: var(--color-dark-destructive-foreground);
    --color-border: var(--color-dark-border);
    --color-input: var(--color-dark-input);
    --color-ring: var(--color-dark-ring);
  }
}

/* Legacy CSS variables for backward compatibility */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.375rem; /* Compact design */
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

/* Modern animations for enhanced UI */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-blob-delay-2 {
  animation: blob 7s infinite;
  animation-delay: 2s;
}

.animate-blob-delay-4 {
  animation: blob 7s infinite;
  animation-delay: 4s;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

/* Enhanced animations for modern UI */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-soft {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

/* Custom animation delays for blob effect - moved to Tailwind config */

/* Glass morphism utilities */
.glass {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

.glass-dark {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(17, 25, 40, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

/* Enhanced focus styles for accessibility */
.focus-enhanced:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern button hover effects */
.btn-glow:hover {
  box-shadow: 0 0 20px hsl(var(--primary) / 0.4);
}
