import type { MetaFunction } from "@remix-run/cloudflare";
import { <PERSON> } from "@remix-run/react";
import { Button } from "@repo/ui-kit";
import { useState } from "react";

export const meta: MetaFunction = () => {
  return [
    { title: "Documentation - AI Chat Platform" },
    {
      name: "description",
      content: "Complete documentation and guides for getting started with AI Chat platform",
    },
  ];
};

export default function Docs() {
  const [activeSection, setActiveSection] =
    useState<keyof typeof contentSections>("getting-started");

  const sidebarSections = [
    {
      title: "Getting Started",
      items: [
        { id: "getting-started", title: "Quick Start Guide", icon: "🚀" },
        { id: "authentication", title: "Authentication", icon: "🔐" },
        { id: "first-chat", title: "Your First Chat", icon: "💬" },
      ],
    },
    {
      title: "Core Features",
      items: [
        { id: "chat-interface", title: "Chat Interface", icon: "🎯" },
        { id: "message-types", title: "Message Types", icon: "📝" },
        { id: "conversation-history", title: "Conversation History", icon: "📚" },
      ],
    },
    {
      title: "Advanced Features",
      items: [
        { id: "custom-prompts", title: "Custom Prompts", icon: "⚡" },
        { id: "integrations", title: "Integrations", icon: "🔗" },
        { id: "api-reference", title: "API Reference", icon: "🛠️" },
      ],
    },
    {
      title: "Troubleshooting",
      items: [
        { id: "common-issues", title: "Common Issues", icon: "❓" },
        { id: "support", title: "Support", icon: "🆘" },
        { id: "changelog", title: "Changelog", icon: "📋" },
      ],
    },
  ];

  const contentSections = {
    "getting-started": {
      title: "Quick Start Guide",
      content: (
        <div className="prose max-w-none">
          <p className="text-lg text-gray-600 mb-6">
            Welcome to AI Chat! This guide will help you get started with our platform in just a few
            minutes.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">1. Create Your Account</h3>
          <p className="mb-4">
            Sign up for a free account to get started. You can use your email or sign in with your
            existing Google or GitHub account.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <p className="text-blue-800">
              <strong>Tip:</strong> No credit card required for the free tier. You get 100 free
              messages per month.
            </p>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">
            2. Start Your First Conversation
          </h3>
          <p className="mb-4">
            Once you're logged in, you'll see the chat interface. Simply type your message in the
            input box and press Enter or click Send.
          </p>
          <div className="bg-gray-100 rounded-lg p-4 mb-6">
            <code className="text-sm">
              Try asking: "Hello! Can you help me understand how this platform works?"
            </code>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">3. Explore Features</h3>
          <ul className="list-disc list-inside mb-6 space-y-2">
            <li>Browse your conversation history in the sidebar</li>
            <li>Use the settings to customize your experience</li>
            <li>Try different types of questions and tasks</li>
          </ul>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Next Steps</h3>
          <p className="mb-4">Ready to dive deeper? Check out these sections:</p>
          <div className="grid md:grid-cols-2 gap-4">
            <button
              onClick={() => setActiveSection("authentication")}
              className="bg-white border border-gray-200 rounded-lg p-4 text-left hover:shadow-md transition-shadow"
            >
              <h4 className="font-semibold text-gray-900 mb-2">🔐 Authentication</h4>
              <p className="text-sm text-gray-600">
                Learn about account security and login options
              </p>
            </button>
            <button
              onClick={() => setActiveSection("chat-interface")}
              className="bg-white border border-gray-200 rounded-lg p-4 text-left hover:shadow-md transition-shadow"
            >
              <h4 className="font-semibold text-gray-900 mb-2">🎯 Chat Interface</h4>
              <p className="text-sm text-gray-600">Master all the features of the chat interface</p>
            </button>
          </div>
        </div>
      ),
    },
    authentication: {
      title: "Authentication",
      content: (
        <div className="prose max-w-none">
          <p className="text-lg text-gray-600 mb-6">
            Learn about the various authentication methods and security features available on our
            platform.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Sign Up Options</h3>
          <div className="space-y-4 mb-6">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">📧 Email & Password</h4>
              <p className="text-gray-600">
                Create an account with your email address and a secure password.
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">🔗 OAuth Providers</h4>
              <p className="text-gray-600">
                Sign in quickly with Google, GitHub, or other supported providers.
              </p>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Security Features</h3>
          <ul className="list-disc list-inside mb-6 space-y-2">
            <li>End-to-end encryption for all conversations</li>
            <li>Two-factor authentication (2FA) available</li>
            <li>Session management and automatic logout</li>
            <li>Regular security audits and updates</li>
          </ul>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-yellow-800">
              <strong>Security Tip:</strong> Always use a strong, unique password and enable 2FA for
              maximum security.
            </p>
          </div>
        </div>
      ),
    },
    "chat-interface": {
      title: "Chat Interface",
      content: (
        <div className="prose max-w-none">
          <p className="text-lg text-gray-600 mb-6">
            Master all the features and capabilities of our intuitive chat interface.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Interface Overview</h3>
          <div className="grid md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Left Sidebar</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>Navigation menu</li>
                <li>Conversation history</li>
                <li>Settings access</li>
                <li>User profile</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Main Chat Area</h4>
              <ul className="list-disc list-inside space-y-1 text-gray-600">
                <li>Message bubbles</li>
                <li>Timestamps</li>
                <li>Message status indicators</li>
                <li>Input field and send button</li>
              </ul>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Keyboard Shortcuts</h3>
          <div className="bg-gray-100 rounded-lg p-4 mb-6">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <p className="font-semibold mb-2">Message Actions</p>
                <ul className="space-y-1 text-sm">
                  <li>
                    <code>Enter</code> - Send message
                  </li>
                  <li>
                    <code>Shift + Enter</code> - New line
                  </li>
                  <li>
                    <code>Ctrl + K</code> - Clear chat
                  </li>
                </ul>
              </div>
              <div>
                <p className="font-semibold mb-2">Navigation</p>
                <ul className="space-y-1 text-sm">
                  <li>
                    <code>Ctrl + 1</code> - Go to chat
                  </li>
                  <li>
                    <code>Ctrl + 2</code> - View history
                  </li>
                  <li>
                    <code>Ctrl + ,</code> - Open settings
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Message Features</h3>
          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">📎 File Attachments</h4>
              <p className="text-gray-600">
                Upload images, documents, and other files to include in your conversations.
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">🎨 Code Highlighting</h4>
              <p className="text-gray-600">
                Automatic syntax highlighting for code snippets in multiple programming languages.
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">📊 Rich Responses</h4>
              <p className="text-gray-600">
                AI can generate tables, lists, and formatted text for better readability.
              </p>
            </div>
          </div>
        </div>
      ),
    },
    "api-reference": {
      title: "API Reference",
      content: (
        <div className="prose max-w-none">
          <p className="text-lg text-gray-600 mb-6">
            Comprehensive API documentation for developers integrating with AI Chat.
          </p>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Authentication</h3>
          <p className="mb-4">All API requests require authentication using API keys:</p>
          <div className="bg-gray-900 text-gray-100 rounded-lg p-4 mb-6">
            <code className="text-sm">
              curl -H "Authorization: Bearer YOUR_API_KEY" \<br />
              &nbsp;&nbsp;&nbsp;&nbsp; https://api.aichat.com/v1/chat
            </code>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4">Endpoints</h3>

          <div className="space-y-6">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs mr-2">
                  POST
                </span>
                /v1/chat
              </h4>
              <p className="text-gray-600 mb-3">Send a message and receive an AI response</p>
              <div className="bg-gray-100 rounded p-3">
                <p className="text-sm font-semibold mb-2">Request Body:</p>
                <pre className="text-xs overflow-x-auto">
                  {`{
  "message": "Hello, how are you?",
  "conversation_id": "optional-uuid",
  "model": "gpt-4",
  "stream": false
}`}
                </pre>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2">
                  GET
                </span>
                /v1/conversations
              </h4>
              <p className="text-gray-600 mb-3">Retrieve conversation history</p>
              <div className="bg-gray-100 rounded p-3">
                <p className="text-sm font-semibold mb-2">Query Parameters:</p>
                <ul className="text-xs space-y-1">
                  <li>
                    <code>limit</code> - Number of conversations to return (default: 20)
                  </li>
                  <li>
                    <code>offset</code> - Pagination offset (default: 0)
                  </li>
                </ul>
              </div>
            </div>

            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">
                <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-xs mr-2">
                  DELETE
                </span>
                /v1/conversations/{"{id}"}
              </h4>
              <p className="text-gray-600">Delete a specific conversation</p>
            </div>
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-4 mt-8">Rate Limits</h3>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <ul className="list-disc list-inside space-y-1 text-yellow-800">
              <li>Free tier: 100 requests per hour</li>
              <li>Pro tier: 1,000 requests per hour</li>
              <li>Enterprise: Custom limits available</li>
            </ul>
          </div>
        </div>
      ),
    },
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/landing" className="text-2xl font-bold text-gray-900">
                AI Chat
              </Link>
            </div>
            <div className="flex items-center space-x-6">
              <Link to="/about" className="text-gray-600 hover:text-gray-900">
                About
              </Link>
              <Link to="/blog" className="text-gray-600 hover:text-gray-900">
                Blog
              </Link>
              <Link to="/docs" className="text-blue-600 font-medium">
                Docs
              </Link>
              <Link to="/login" className="text-gray-600 hover:text-gray-900">
                Login
              </Link>
              <Link to="/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-gray-50 min-h-screen border-r border-gray-200">
          <div className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Documentation</h2>
            <nav className="space-y-6">
              {sidebarSections.map((section) => (
                <div key={section.title}>
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">
                    {section.title}
                  </h3>
                  <ul className="space-y-2">
                    {section.items.map((item) => (
                      <li key={item.id}>
                        <button
                          onClick={() => setActiveSection(item.id as keyof typeof contentSections)}
                          className={`w-full text-left flex items-center px-3 py-2 text-sm rounded-lg transition-colors ${
                            activeSection === item.id
                              ? "bg-blue-100 text-blue-700 font-medium"
                              : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                          }`}
                        >
                          <span className="mr-3">{item.icon}</span>
                          {item.title}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </nav>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1">
          <div className="max-w-4xl mx-auto px-6 py-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {contentSections[activeSection]?.title}
              </h1>
            </div>

            <div className="bg-white">{contentSections[activeSection]?.content}</div>

            {/* Footer Navigation */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500 mb-2">Need help?</p>
                  <Link
                    to="/blog"
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Check our blog for tutorials →
                  </Link>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500 mb-2">Ready to start?</p>
                  <Link to="/landing">
                    <Button>Try AI Chat Now</Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
