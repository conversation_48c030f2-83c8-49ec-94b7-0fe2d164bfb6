import * as React from "react";
import { cn } from "../../utils/index.js";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        data-slot="input"
        className={cn(
          // Base styles
          "flex w-full min-w-0 rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-all duration-200 outline-none",
          // Text and placeholder styles
          "text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground",
          // File input styles
          "file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
          // Focus styles
          "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-2",
          // Invalid/error styles
          "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
          // Disabled styles
          "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
          // Height and responsive text
          "h-10 md:text-sm",
          // Default border and background
          "border-input bg-background",
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };
