import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";
import { getAuth } from "~/lib/auth.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  const auth = getAuth(context);
  const url = new URL(request.url);

  try {
    const result = await auth.handler(request);

    if (result.status === 302) {
      // Follow redirect
      return result;
    }

    // Default redirect to dashboard
    return redirect("/dashboard");
  } catch (error) {
    console.error("OAuth callback error:", error);
    return redirect("/login?error=oauth_callback_failed");
  }
}
